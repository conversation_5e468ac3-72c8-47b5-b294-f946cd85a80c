#!/usr/bin/env python3
"""
Export and Reporting Manager for Psychiatric Assessment System
Implements secure data export, PDF reports, and HIPAA-compliant data handling
"""

import streamlit as st
import pandas as pd
import json
import io
import base64
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
import zipfile
import tempfile
import os

# PDF generation imports
try:
    from reportlab.lib.pagesizes import letter, A4
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, PageBreak
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.lib import colors
    from reportlab.graphics.shapes import Drawing
    from reportlab.graphics.charts.barcharts import VerticalBarChart
    from reportlab.graphics.charts.piecharts import Pie
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False

from security_manager import security_manager
from auth_manager import auth_manager, Permission

class ExportManager:
    """Secure export and reporting manager"""
    
    def __init__(self):
        self.max_export_records = 10000  # Limit for security
        self.allowed_formats = ['csv', 'json', 'pdf', 'xlsx']
        
    def check_export_permission(self) -> bool:
        """Check if user has export permission"""
        return auth_manager.require_permission(Permission.EXPORT_DATA)
    
    def log_export_activity(self, export_type: str, record_count: int, filters: Dict[str, Any]):
        """Log export activity for audit"""
        current_user = auth_manager.get_current_user()
        if current_user:
            details = {
                'export_type': export_type,
                'record_count': record_count,
                'filters': filters,
                'timestamp': datetime.now().isoformat()
            }
            
            auth_manager.log_audit_event(
                current_user['user_id'],
                "DATA_EXPORT",
                "export",
                json.dumps(details)
            )
    
    def anonymize_for_export(self, df: pd.DataFrame, anonymize_level: str = "partial") -> pd.DataFrame:
        """Anonymize data for export based on level"""
        if df.empty:
            return df
        
        anonymized_df = df.copy()
        
        if anonymize_level == "full":
            # Full anonymization - remove all identifiers
            sensitive_columns = ['patient_id', 'name', 'email', 'phone', 'address']
            for col in sensitive_columns:
                if col in anonymized_df.columns:
                    anonymized_df[col] = anonymized_df[col].apply(
                        lambda x: security_manager.mask_sensitive_data(str(x)) if pd.notna(x) else x
                    )
        
        elif anonymize_level == "partial":
            # Partial anonymization - mask but keep some structure
            if 'patient_id' in anonymized_df.columns:
                anonymized_df['patient_id'] = anonymized_df['patient_id'].apply(
                    lambda x: f"PATIENT-{hash(str(x)) % 10000:04d}" if pd.notna(x) else x
                )
        
        # Always remove or hash highly sensitive fields
        if 'data_json' in anonymized_df.columns:
            # Remove detailed assessment data for exports
            anonymized_df = anonymized_df.drop('data_json', axis=1)
        
        return anonymized_df
    
    def export_to_csv(self, df: pd.DataFrame, filename: str, anonymize: bool = True) -> bytes:
        """Export dataframe to CSV format"""
        if not self.check_export_permission():
            raise PermissionError("You don't have permission to export data")
        
        if len(df) > self.max_export_records:
            raise ValueError(f"Export limited to {self.max_export_records} records")
        
        # Anonymize if requested
        if anonymize:
            df = self.anonymize_for_export(df, "partial")
        
        # Convert to CSV
        csv_buffer = io.StringIO()
        df.to_csv(csv_buffer, index=False)
        
        return csv_buffer.getvalue().encode('utf-8')
    
    def export_to_json(self, df: pd.DataFrame, filename: str, anonymize: bool = True) -> bytes:
        """Export dataframe to JSON format"""
        if not self.check_export_permission():
            raise PermissionError("You don't have permission to export data")
        
        if len(df) > self.max_export_records:
            raise ValueError(f"Export limited to {self.max_export_records} records")
        
        # Anonymize if requested
        if anonymize:
            df = self.anonymize_for_export(df, "partial")
        
        # Convert to JSON
        json_data = df.to_json(orient='records', date_format='iso', indent=2)
        
        return json_data.encode('utf-8')
    
    def export_to_excel(self, df: pd.DataFrame, filename: str, anonymize: bool = True) -> bytes:
        """Export dataframe to Excel format"""
        if not self.check_export_permission():
            raise PermissionError("You don't have permission to export data")
        
        if len(df) > self.max_export_records:
            raise ValueError(f"Export limited to {self.max_export_records} records")
        
        # Anonymize if requested
        if anonymize:
            df = self.anonymize_for_export(df, "partial")
        
        # Convert to Excel
        excel_buffer = io.BytesIO()
        with pd.ExcelWriter(excel_buffer, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='Assessment_Data', index=False)
            
            # Add metadata sheet
            metadata = pd.DataFrame({
                'Export_Info': [
                    'Export Date',
                    'Total Records',
                    'Exported By',
                    'Anonymization Level',
                    'Data Source'
                ],
                'Value': [
                    datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    len(df),
                    auth_manager.get_current_user()['username'],
                    'Partial' if anonymize else 'None',
                    'Psychiatric Assessment System'
                ]
            })
            metadata.to_excel(writer, sheet_name='Export_Metadata', index=False)
        
        return excel_buffer.getvalue()
    
    def generate_assessment_report_pdf(self, assessment_data: Dict[str, Any], patient_data: Dict[str, Any]) -> bytes:
        """Generate PDF report for individual assessment"""
        if not REPORTLAB_AVAILABLE:
            raise ImportError("ReportLab not available for PDF generation")
        
        if not self.check_export_permission():
            raise PermissionError("You don't have permission to generate reports")
        
        # Create PDF buffer
        pdf_buffer = io.BytesIO()
        doc = SimpleDocTemplate(pdf_buffer, pagesize=letter)
        
        # Get styles
        styles = getSampleStyleSheet()
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=18,
            spaceAfter=30,
            alignment=1  # Center alignment
        )
        
        # Build PDF content
        story = []
        
        # Title
        story.append(Paragraph("Psychiatric Assessment Report", title_style))
        story.append(Spacer(1, 20))
        
        # Patient Information (anonymized)
        patient_id = patient_data.get('patient_id', 'Unknown')
        anonymized_id = f"PATIENT-{hash(patient_id) % 10000:04d}"
        
        story.append(Paragraph("Patient Information", styles['Heading2']))
        patient_info = [
            ['Patient ID:', anonymized_id],
            ['Assessment Date:', datetime.now().strftime('%Y-%m-%d')],
            ['Age:', patient_data.get('age', 'Not specified')],
            ['Gender:', patient_data.get('gender', 'Not specified')]
        ]
        
        patient_table = Table(patient_info, colWidths=[2*inch, 3*inch])
        patient_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, -1), colors.lightgrey),
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
        ]))
        story.append(patient_table)
        story.append(Spacer(1, 20))
        
        # Assessment Summary
        story.append(Paragraph("Assessment Summary", styles['Heading2']))
        
        # Risk Assessment
        risk_data = assessment_data.get('risk_assessment', {})
        suicide_risk = risk_data.get('suicide', {}).get('risk_level', 'Not assessed')
        
        story.append(Paragraph(f"<b>Suicide Risk Level:</b> {suicide_risk}", styles['Normal']))
        story.append(Spacer(1, 10))
        
        # Clinical Scales
        scales_data = assessment_data.get('clinical_scales', {})
        phq9_score = scales_data.get('depression', {}).get('phq9_total', 'Not completed')
        gad7_score = scales_data.get('anxiety', {}).get('gad7_total', 'Not completed')
        
        story.append(Paragraph(f"<b>PHQ-9 Score:</b> {phq9_score}", styles['Normal']))
        story.append(Paragraph(f"<b>GAD-7 Score:</b> {gad7_score}", styles['Normal']))
        story.append(Spacer(1, 20))
        
        # Diagnostic Formulation
        diagnostic_data = assessment_data.get('diagnostic_formulation', {})
        primary_diagnosis = diagnostic_data.get('diagnoses', {}).get('primary', 'Not specified')
        
        story.append(Paragraph("Diagnostic Formulation", styles['Heading2']))
        story.append(Paragraph(f"<b>Primary Diagnosis:</b> {primary_diagnosis}", styles['Normal']))
        story.append(Spacer(1, 20))
        
        # Treatment Planning
        treatment_data = assessment_data.get('treatment_planning', {})
        if treatment_data:
            story.append(Paragraph("Treatment Recommendations", styles['Heading2']))
            
            # Add treatment recommendations if available
            recommendations = treatment_data.get('recommendations', 'No specific recommendations documented')
            story.append(Paragraph(recommendations, styles['Normal']))
        
        # Footer
        story.append(Spacer(1, 40))
        story.append(Paragraph("This report is confidential and intended for authorized healthcare providers only.", 
                              styles['Italic']))
        story.append(Paragraph(f"Generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} by Psychiatric Assessment System", 
                              styles['Italic']))
        
        # Build PDF
        doc.build(story)
        
        return pdf_buffer.getvalue()
    
    def create_bulk_export_package(self, assessments_df: pd.DataFrame, patients_df: pd.DataFrame, 
                                 export_format: str = 'csv', anonymize: bool = True) -> bytes:
        """Create a bulk export package with multiple files"""
        if not self.check_export_permission():
            raise PermissionError("You don't have permission to export data")
        
        # Create ZIP file in memory
        zip_buffer = io.BytesIO()
        
        with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
            # Export assessments
            if not assessments_df.empty:
                if export_format == 'csv':
                    assessments_data = self.export_to_csv(assessments_df, 'assessments.csv', anonymize)
                    zip_file.writestr('assessments.csv', assessments_data)
                elif export_format == 'json':
                    assessments_data = self.export_to_json(assessments_df, 'assessments.json', anonymize)
                    zip_file.writestr('assessments.json', assessments_data)
                elif export_format == 'xlsx':
                    assessments_data = self.export_to_excel(assessments_df, 'assessments.xlsx', anonymize)
                    zip_file.writestr('assessments.xlsx', assessments_data)
            
            # Export patients
            if not patients_df.empty:
                if export_format == 'csv':
                    patients_data = self.export_to_csv(patients_df, 'patients.csv', anonymize)
                    zip_file.writestr('patients.csv', patients_data)
                elif export_format == 'json':
                    patients_data = self.export_to_json(patients_df, 'patients.json', anonymize)
                    zip_file.writestr('patients.json', patients_data)
                elif export_format == 'xlsx':
                    patients_data = self.export_to_excel(patients_df, 'patients.xlsx', anonymize)
                    zip_file.writestr('patients.xlsx', patients_data)
            
            # Add export metadata
            metadata = {
                'export_timestamp': datetime.now().isoformat(),
                'exported_by': auth_manager.get_current_user()['username'],
                'total_assessments': len(assessments_df),
                'total_patients': len(patients_df),
                'anonymization_level': 'partial' if anonymize else 'none',
                'export_format': export_format,
                'system_version': '2.0'
            }
            
            zip_file.writestr('export_metadata.json', json.dumps(metadata, indent=2))
        
        return zip_buffer.getvalue()
    
    def show_export_interface(self, assessments_df: pd.DataFrame, patients_df: pd.DataFrame):
        """Display export interface in Streamlit"""
        st.markdown('<div class="section-header">📥 Data Export</div>', unsafe_allow_html=True)
        
        if not self.check_export_permission():
            st.error("You don't have permission to export data")
            return
        
        # Export options
        col1, col2, col3 = st.columns(3)
        
        with col1:
            export_format = st.selectbox(
                "Export Format",
                ['csv', 'json', 'xlsx'],
                help="Choose the format for data export"
            )
        
        with col2:
            anonymize_data = st.checkbox(
                "Anonymize Data",
                value=True,
                help="Remove or mask sensitive identifiers"
            )
        
        with col3:
            include_patients = st.checkbox(
                "Include Patient Demographics",
                value=True,
                help="Include patient demographic data in export"
            )
        
        # Export buttons
        col1, col2, col3 = st.columns(3)
        
        with col1:
            if st.button("📊 Export Assessments"):
                try:
                    if export_format == 'csv':
                        data = self.export_to_csv(assessments_df, 'assessments.csv', anonymize_data)
                        mime_type = 'text/csv'
                    elif export_format == 'json':
                        data = self.export_to_json(assessments_df, 'assessments.json', anonymize_data)
                        mime_type = 'application/json'
                    elif export_format == 'xlsx':
                        data = self.export_to_excel(assessments_df, 'assessments.xlsx', anonymize_data)
                        mime_type = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                    
                    filename = f"assessments_{datetime.now().strftime('%Y%m%d_%H%M%S')}.{export_format}"
                    
                    st.download_button(
                        label=f"Download {export_format.upper()}",
                        data=data,
                        file_name=filename,
                        mime=mime_type
                    )
                    
                    self.log_export_activity('assessments', len(assessments_df), {'format': export_format})
                    st.success(f"Assessment data exported successfully ({len(assessments_df)} records)")
                    
                except Exception as e:
                    st.error(f"Export failed: {str(e)}")
        
        with col2:
            if st.button("📋 Export Complete Package"):
                try:
                    export_data = assessments_df if not include_patients else assessments_df
                    patient_data = patients_df if include_patients else pd.DataFrame()
                    
                    zip_data = self.create_bulk_export_package(
                        export_data, patient_data, export_format, anonymize_data
                    )
                    
                    filename = f"psychiatric_data_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.zip"
                    
                    st.download_button(
                        label="Download ZIP Package",
                        data=zip_data,
                        file_name=filename,
                        mime='application/zip'
                    )
                    
                    self.log_export_activity('bulk_package', len(assessments_df), {
                        'format': export_format,
                        'include_patients': include_patients
                    })
                    st.success("Complete data package exported successfully")
                    
                except Exception as e:
                    st.error(f"Export failed: {str(e)}")
        
        with col3:
            if st.button("📄 Generate PDF Report") and REPORTLAB_AVAILABLE:
                st.info("PDF report generation available for individual assessments")

# Global export manager instance
export_manager = ExportManager()
