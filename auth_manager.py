#!/usr/bin/env python3
"""
Authentication and Authorization Manager for Psychiatric Assessment System
Implements user registration, login, role-based access control, and session management
"""

import streamlit as st
import sqlite3
import hashlib
import secrets
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
import logging
import json
from enum import Enum
from security_manager import security_manager, secure_db

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class UserRole(Enum):
    """User roles with different access levels"""
    ADMIN = "admin"
    CLINICIAN = "clinician"
    VIEWER = "viewer"

class Permission(Enum):
    """System permissions"""
    READ_PATIENTS = "read_patients"
    WRITE_PATIENTS = "write_patients"
    DELETE_PATIENTS = "delete_patients"
    READ_ASSESSMENTS = "read_assessments"
    WRITE_ASSESSMENTS = "write_assessments"
    DELETE_ASSESSMENTS = "delete_assessments"
    EXPORT_DATA = "export_data"
    MANAGE_USERS = "manage_users"
    VIEW_AUDIT_LOGS = "view_audit_logs"
    SYSTEM_CONFIG = "system_config"

class AuthenticationManager:
    """Handles user authentication and session management"""
    
    def __init__(self):
        self.session_timeout = 30 * 60  # 30 minutes in seconds
        self.max_login_attempts = 5
        self.lockout_duration = 15 * 60  # 15 minutes
        self._init_auth_tables()
        
        # Role permissions mapping
        self.role_permissions = {
            UserRole.ADMIN: [
                Permission.READ_PATIENTS, Permission.WRITE_PATIENTS, Permission.DELETE_PATIENTS,
                Permission.READ_ASSESSMENTS, Permission.WRITE_ASSESSMENTS, Permission.DELETE_ASSESSMENTS,
                Permission.EXPORT_DATA, Permission.MANAGE_USERS, Permission.VIEW_AUDIT_LOGS,
                Permission.SYSTEM_CONFIG
            ],
            UserRole.CLINICIAN: [
                Permission.READ_PATIENTS, Permission.WRITE_PATIENTS,
                Permission.READ_ASSESSMENTS, Permission.WRITE_ASSESSMENTS,
                Permission.EXPORT_DATA
            ],
            UserRole.VIEWER: [
                Permission.READ_PATIENTS, Permission.READ_ASSESSMENTS
            ]
        }
    
    def _init_auth_tables(self):
        """Initialize authentication tables"""
        try:
            # Users table
            secure_db.execute_query('''
                CREATE TABLE IF NOT EXISTS users (
                    user_id TEXT PRIMARY KEY,
                    username TEXT UNIQUE NOT NULL,
                    email TEXT UNIQUE NOT NULL,
                    password_hash TEXT NOT NULL,
                    salt TEXT NOT NULL,
                    role TEXT NOT NULL,
                    is_active BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_login TIMESTAMP,
                    failed_login_attempts INTEGER DEFAULT 0,
                    locked_until TIMESTAMP,
                    password_changed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Sessions table
            secure_db.execute_query('''
                CREATE TABLE IF NOT EXISTS user_sessions (
                    session_id TEXT PRIMARY KEY,
                    user_id TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    expires_at TIMESTAMP NOT NULL,
                    is_active BOOLEAN DEFAULT TRUE,
                    ip_address TEXT,
                    user_agent TEXT,
                    FOREIGN KEY (user_id) REFERENCES users (user_id)
                )
            ''')
            
            # Audit log table
            secure_db.execute_query('''
                CREATE TABLE IF NOT EXISTS audit_log (
                    log_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT,
                    action TEXT NOT NULL,
                    resource TEXT,
                    details TEXT,
                    ip_address TEXT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    success BOOLEAN DEFAULT TRUE
                )
            ''')
            
            # Create default admin user if no users exist
            self._create_default_admin()
            
        except Exception as e:
            logger.error(f"Failed to initialize auth tables: {e}")
            raise
    
    def _create_default_admin(self):
        """Create default admin user if no users exist"""
        try:
            result = secure_db.execute_query("SELECT COUNT(*) FROM users", fetch="one")
            if result and result[0] == 0:
                # Create default admin
                password_hash, salt = security_manager.hash_password("admin123")
                user_id = security_manager.generate_secure_token(16)
                
                secure_db.execute_query('''
                    INSERT INTO users (user_id, username, email, password_hash, salt, role)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (user_id, "admin", "<EMAIL>", password_hash, salt, UserRole.ADMIN.value))
                
                logger.info("Default admin user created (username: admin, password: admin123)")
                
        except Exception as e:
            logger.error(f"Failed to create default admin: {e}")
    
    def register_user(self, username: str, email: str, password: str, role: UserRole) -> Tuple[bool, str]:
        """Register a new user"""
        try:
            # Validate inputs
            if not username or len(username) < 3:
                return False, "Username must be at least 3 characters"
            
            if not security_manager.validate_email(email):
                return False, "Invalid email format"
            
            if len(password) < 8:
                return False, "Password must be at least 8 characters"
            
            # Check if username or email already exists
            existing_user = secure_db.execute_query(
                "SELECT user_id FROM users WHERE username = ? OR email = ?",
                (username, email),
                fetch="one"
            )
            
            if existing_user:
                return False, "Username or email already exists"
            
            # Hash password
            password_hash, salt = security_manager.hash_password(password)
            user_id = security_manager.generate_secure_token(16)
            
            # Insert user
            secure_db.execute_query('''
                INSERT INTO users (user_id, username, email, password_hash, salt, role)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (user_id, username, email, password_hash, salt, role.value))
            
            self.log_audit_event(None, "USER_REGISTERED", "users", f"New user registered: {username}")
            return True, "User registered successfully"
            
        except Exception as e:
            logger.error(f"User registration failed: {e}")
            return False, "Registration failed"
    
    def authenticate_user(self, username: str, password: str) -> Tuple[bool, Optional[Dict[str, Any]], str]:
        """Authenticate user credentials"""
        try:
            # Get user data
            user_data = secure_db.execute_query(
                "SELECT user_id, username, email, password_hash, salt, role, is_active, failed_login_attempts, locked_until FROM users WHERE username = ?",
                (username,),
                fetch="one"
            )
            
            if not user_data:
                self.log_audit_event(None, "LOGIN_FAILED", "authentication", f"Invalid username: {username}")
                return False, None, "Invalid credentials"
            
            user_id, username, email, password_hash, salt, role, is_active, failed_attempts, locked_until = user_data
            
            # Check if account is active
            if not is_active:
                self.log_audit_event(user_id, "LOGIN_FAILED", "authentication", "Account disabled")
                return False, None, "Account is disabled"
            
            # Check if account is locked
            if locked_until:
                locked_until_dt = datetime.fromisoformat(locked_until)
                if datetime.now() < locked_until_dt:
                    self.log_audit_event(user_id, "LOGIN_FAILED", "authentication", "Account locked")
                    return False, None, f"Account locked until {locked_until_dt.strftime('%Y-%m-%d %H:%M:%S')}"
            
            # Verify password
            if not security_manager.verify_password(password, password_hash, salt):
                # Increment failed attempts
                failed_attempts += 1
                locked_until = None
                
                if failed_attempts >= self.max_login_attempts:
                    locked_until = (datetime.now() + timedelta(seconds=self.lockout_duration)).isoformat()
                
                secure_db.execute_query(
                    "UPDATE users SET failed_login_attempts = ?, locked_until = ? WHERE user_id = ?",
                    (failed_attempts, locked_until, user_id)
                )
                
                self.log_audit_event(user_id, "LOGIN_FAILED", "authentication", f"Invalid password (attempt {failed_attempts})")
                return False, None, "Invalid credentials"
            
            # Reset failed attempts on successful login
            secure_db.execute_query(
                "UPDATE users SET failed_login_attempts = 0, locked_until = NULL, last_login = CURRENT_TIMESTAMP WHERE user_id = ?",
                (user_id,)
            )
            
            user_info = {
                'user_id': user_id,
                'username': username,
                'email': email,
                'role': UserRole(role)
            }
            
            self.log_audit_event(user_id, "LOGIN_SUCCESS", "authentication", f"User logged in: {username}")
            return True, user_info, "Login successful"
            
        except Exception as e:
            logger.error(f"Authentication failed: {e}")
            return False, None, "Authentication error"
    
    def create_session(self, user_info: Dict[str, Any]) -> str:
        """Create a new user session"""
        try:
            session_id = security_manager.generate_secure_token(32)
            expires_at = (datetime.now() + timedelta(seconds=self.session_timeout)).isoformat()
            
            # Get client info (if available)
            ip_address = st.session_state.get('client_ip', 'unknown')
            user_agent = st.session_state.get('user_agent', 'unknown')
            
            secure_db.execute_query('''
                INSERT INTO user_sessions (session_id, user_id, expires_at, ip_address, user_agent)
                VALUES (?, ?, ?, ?, ?)
            ''', (session_id, user_info['user_id'], expires_at, ip_address, user_agent))
            
            # Store session in Streamlit session state
            st.session_state['session_id'] = session_id
            st.session_state['user_info'] = user_info
            st.session_state['session_expires'] = expires_at
            
            self.log_audit_event(user_info['user_id'], "SESSION_CREATED", "sessions", f"Session created: {session_id[:8]}...")
            return session_id
            
        except Exception as e:
            logger.error(f"Session creation failed: {e}")
            raise
    
    def validate_session(self, session_id: str) -> Tuple[bool, Optional[Dict[str, Any]]]:
        """Validate user session"""
        try:
            session_data = secure_db.execute_query('''
                SELECT s.user_id, s.expires_at, s.is_active, u.username, u.email, u.role, u.is_active as user_active
                FROM user_sessions s
                JOIN users u ON s.user_id = u.user_id
                WHERE s.session_id = ?
            ''', (session_id,), fetch="one")
            
            if not session_data:
                return False, None
            
            user_id, expires_at, session_active, username, email, role, user_active = session_data
            
            # Check if session is active
            if not session_active or not user_active:
                return False, None
            
            # Check if session has expired
            expires_dt = datetime.fromisoformat(expires_at)
            if datetime.now() > expires_dt:
                self.invalidate_session(session_id)
                return False, None
            
            # Extend session
            new_expires = (datetime.now() + timedelta(seconds=self.session_timeout)).isoformat()
            secure_db.execute_query(
                "UPDATE user_sessions SET expires_at = ? WHERE session_id = ?",
                (new_expires, session_id)
            )
            
            user_info = {
                'user_id': user_id,
                'username': username,
                'email': email,
                'role': UserRole(role)
            }
            
            return True, user_info
            
        except Exception as e:
            logger.error(f"Session validation failed: {e}")
            return False, None
    
    def invalidate_session(self, session_id: str):
        """Invalidate a user session"""
        try:
            secure_db.execute_query(
                "UPDATE user_sessions SET is_active = FALSE WHERE session_id = ?",
                (session_id,)
            )
            
            # Clear Streamlit session state
            if 'session_id' in st.session_state:
                del st.session_state['session_id']
            if 'user_info' in st.session_state:
                del st.session_state['user_info']
            if 'session_expires' in st.session_state:
                del st.session_state['session_expires']
            
            self.log_audit_event(None, "SESSION_INVALIDATED", "sessions", f"Session invalidated: {session_id[:8]}...")
            
        except Exception as e:
            logger.error(f"Session invalidation failed: {e}")
    
    def logout_user(self):
        """Logout current user"""
        session_id = st.session_state.get('session_id')
        user_info = st.session_state.get('user_info')
        
        if session_id:
            self.invalidate_session(session_id)
        
        if user_info:
            self.log_audit_event(user_info['user_id'], "LOGOUT", "authentication", f"User logged out: {user_info['username']}")
    
    def has_permission(self, user_role: UserRole, permission: Permission) -> bool:
        """Check if user role has specific permission"""
        return permission in self.role_permissions.get(user_role, [])
    
    def require_permission(self, permission: Permission) -> bool:
        """Decorator/function to require specific permission"""
        user_info = st.session_state.get('user_info')
        if not user_info:
            return False
        
        return self.has_permission(user_info['role'], permission)
    
    def log_audit_event(self, user_id: Optional[str], action: str, resource: str, details: str, success: bool = True):
        """Log audit event"""
        try:
            ip_address = st.session_state.get('client_ip', 'unknown')
            
            secure_db.execute_query('''
                INSERT INTO audit_log (user_id, action, resource, details, ip_address, success)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (user_id, action, resource, details, ip_address, success))
            
        except Exception as e:
            logger.error(f"Audit logging failed: {e}")
    
    def get_current_user(self) -> Optional[Dict[str, Any]]:
        """Get current authenticated user"""
        session_id = st.session_state.get('session_id')
        if not session_id:
            return None
        
        is_valid, user_info = self.validate_session(session_id)
        if is_valid:
            return user_info
        
        return None
    
    def is_authenticated(self) -> bool:
        """Check if user is authenticated"""
        return self.get_current_user() is not None

# Global authentication manager instance
auth_manager = AuthenticationManager()
