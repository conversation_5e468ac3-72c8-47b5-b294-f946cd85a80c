#!/usr/bin/env python3
"""
Simple security test to verify our implementations
"""

from security_manager import security_manager

def test_basic_security():
    print("Testing basic security functions...")
    
    # Test SQL injection detection
    sql_injection = "'; DROP TABLE patients; --"
    is_safe = security_manager.validate_sql_input(sql_injection)
    print(f"SQL injection test: {'PASS' if not is_safe else 'FAIL'}")
    
    # Test XSS detection
    xss_attempt = "<script>alert('XSS')</script>"
    is_safe = security_manager.validate_xss_input(xss_attempt)
    print(f"XSS test: {'PASS' if not is_safe else 'FAIL'}")
    
    # Test input sanitization
    malicious_input = "'; DROP TABLE users; --"
    is_valid, sanitized = security_manager.validate_and_sanitize_input(malicious_input)
    print(f"Input sanitization: {'PASS' if not is_valid else 'FAIL'}")
    
    # Test password hashing
    password = "test123"
    hash_result, salt = security_manager.hash_password(password)
    is_valid = security_manager.verify_password(password, hash_result, salt)
    print(f"Password hashing: {'PASS' if is_valid else 'FAIL'}")
    
    # Test encryption
    data = "sensitive data"
    encrypted = security_manager.encrypt_sensitive_data(data)
    decrypted = security_manager.decrypt_sensitive_data(encrypted)
    print(f"Encryption: {'PASS' if data == decrypted else 'FAIL'}")
    
    print("Basic security tests completed!")

if __name__ == "__main__":
    test_basic_security()
