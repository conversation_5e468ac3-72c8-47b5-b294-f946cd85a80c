#!/usr/bin/env python3
"""
Comprehensive Security and Functionality Test Suite
Tests all implemented security fixes and enhancements
"""

import sys
import os
import time
import json
from datetime import datetime

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from security_manager import security_manager, secure_db
from auth_manager import auth_manager, UserRole, Permission
from export_manager import export_manager

class ComprehensiveSecurityTest:
    """Comprehensive test suite for security and functionality"""
    
    def __init__(self):
        self.test_results = []
        self.passed_tests = 0
        self.failed_tests = 0
    
    def log_test(self, test_name: str, passed: bool, message: str = ""):
        """Log test results"""
        status = "✅ PASS" if passed else "❌ FAIL"
        self.test_results.append({
            'test': test_name,
            'passed': passed,
            'message': message,
            'timestamp': datetime.now().isoformat()
        })
        
        if passed:
            self.passed_tests += 1
        else:
            self.failed_tests += 1
        
        print(f"{status}: {test_name}")
        if message:
            print(f"    {message}")
    
    def test_sql_injection_prevention(self):
        """Test comprehensive SQL injection prevention"""
        print("\n🧪 Testing SQL Injection Prevention...")
        
        # SQL injection patterns that should be blocked in user input
        injection_attempts = [
            "'; DROP TABLE patients; --",
            "' OR '1'='1",
            "' UNION SELECT * FROM patients --",
            "' OR 1=1 --",
            "admin'--",
            "' OR 'x'='x",
        ]
        
        blocked_count = 0
        for attempt in injection_attempts:
            is_safe = security_manager.validate_sql_input(attempt)
            if not is_safe:
                blocked_count += 1
        
        success = blocked_count == len(injection_attempts)
        self.log_test(
            "SQL Injection Prevention", 
            success, 
            f"Blocked {blocked_count}/{len(injection_attempts)} injection attempts"
        )
        return success
    
    def test_xss_prevention(self):
        """Test comprehensive XSS prevention"""
        print("\n🧪 Testing XSS Prevention...")
        
        # XSS patterns that should be blocked
        xss_attempts = [
            "<script>alert('XSS')</script>",
            "javascript:alert('XSS')",
            "<img src=x onerror='alert(\"XSS\")'>",
            "<svg onload='alert(\"XSS\")'>",
        ]
        
        blocked_count = 0
        for attempt in xss_attempts:
            is_safe = security_manager.validate_xss_input(attempt)
            if not is_safe:
                blocked_count += 1
        
        success = blocked_count == len(xss_attempts)
        self.log_test(
            "XSS Prevention", 
            success, 
            f"Blocked {blocked_count}/{len(xss_attempts)} XSS attempts"
        )
        return success
    
    def test_authentication_system(self):
        """Test authentication and authorization system"""
        print("\n🧪 Testing Authentication System...")
        
        try:
            # Test user registration with unique username
            import time
            unique_id = str(int(time.time()))
            username = f"test_security_user_{unique_id}"
            email = f"test{unique_id}@security.test"

            success, message = auth_manager.register_user(
                username,
                email,
                "SecurePassword123!",
                UserRole.CLINICIAN
            )

            if not success:
                self.log_test("User Registration", False, f"Registration failed: {message}")
                return False
            
            # Test authentication
            auth_success, user_info, auth_message = auth_manager.authenticate_user(
                username,
                "SecurePassword123!"
            )
            
            if not auth_success:
                self.log_test("User Authentication", False, f"Authentication failed: {auth_message}")
                return False
            
            # Test permission system
            has_read_permission = auth_manager.has_permission(UserRole.CLINICIAN, Permission.READ_PATIENTS)
            has_admin_permission = auth_manager.has_permission(UserRole.CLINICIAN, Permission.MANAGE_USERS)
            
            if not has_read_permission or has_admin_permission:
                self.log_test("Permission System", False, "Permission system not working correctly")
                return False
            
            self.log_test("Authentication System", True, "All authentication tests passed")
            return True
            
        except Exception as e:
            self.log_test("Authentication System", False, f"Exception: {str(e)}")
            return False
    
    def test_data_encryption(self):
        """Test data encryption functionality"""
        print("\n🧪 Testing Data Encryption...")
        
        try:
            # Test sensitive data encryption
            test_data = "PATIENT-SENSITIVE-DATA-12345"
            encrypted = security_manager.encrypt_sensitive_data(test_data)
            decrypted = security_manager.decrypt_sensitive_data(encrypted)
            
            if test_data != decrypted:
                self.log_test("Data Encryption", False, "Encryption/decryption failed")
                return False
            
            # Test password hashing
            password = "TestPassword123!"
            hash_result, salt = security_manager.hash_password(password)
            
            # Verify correct password
            if not security_manager.verify_password(password, hash_result, salt):
                self.log_test("Password Hashing", False, "Password verification failed")
                return False
            
            # Verify incorrect password is rejected
            if security_manager.verify_password("WrongPassword", hash_result, salt):
                self.log_test("Password Hashing", False, "Incorrect password accepted")
                return False
            
            self.log_test("Data Encryption", True, "All encryption tests passed")
            return True
            
        except Exception as e:
            self.log_test("Data Encryption", False, f"Exception: {str(e)}")
            return False
    
    def test_input_validation(self):
        """Test comprehensive input validation"""
        print("\n🧪 Testing Input Validation...")
        
        try:
            # Test patient ID validation
            valid_ids = ["PATIENT-123", "PSY-ABC123", "CASE-001"]
            invalid_ids = ["'; DROP TABLE", "<script>", "../../etc/passwd"]
            
            for valid_id in valid_ids:
                if not security_manager.validate_patient_id(valid_id):
                    self.log_test("Patient ID Validation", False, f"Valid ID rejected: {valid_id}")
                    return False
            
            for invalid_id in invalid_ids:
                if security_manager.validate_patient_id(invalid_id):
                    self.log_test("Patient ID Validation", False, f"Invalid ID accepted: {invalid_id}")
                    return False
            
            # Test age validation
            valid_ages = [25, 45, 80]
            invalid_ages = [-5, 200, "abc"]
            
            for age in valid_ages:
                if not security_manager.validate_age(age):
                    self.log_test("Age Validation", False, f"Valid age rejected: {age}")
                    return False
            
            for age in invalid_ages:
                if security_manager.validate_age(age):
                    self.log_test("Age Validation", False, f"Invalid age accepted: {age}")
                    return False
            
            # Test email validation
            valid_emails = ["<EMAIL>", "<EMAIL>"]
            invalid_emails = ["invalid-email", "@domain.com", "user@"]
            
            for email in valid_emails:
                if not security_manager.validate_email(email):
                    self.log_test("Email Validation", False, f"Valid email rejected: {email}")
                    return False
            
            for email in invalid_emails:
                if security_manager.validate_email(email):
                    self.log_test("Email Validation", False, f"Invalid email accepted: {email}")
                    return False
            
            self.log_test("Input Validation", True, "All validation tests passed")
            return True
            
        except Exception as e:
            self.log_test("Input Validation", False, f"Exception: {str(e)}")
            return False
    
    def test_secure_database_operations(self):
        """Test secure database operations"""
        print("\n🧪 Testing Secure Database Operations...")
        
        try:
            # Test parameterized query execution
            test_query = "SELECT COUNT(*) FROM users WHERE username = ?"
            result = secure_db.execute_query(test_query, ("test_user",), fetch="one")
            
            # Test that dangerous parameters are rejected
            try:
                dangerous_param = "'; DROP TABLE users; --"
                secure_db.execute_query("SELECT COUNT(*) FROM users WHERE username = ?", (dangerous_param,), fetch="one")
                self.log_test("Secure Database", False, "Dangerous parameter was allowed")
                return False
            except ValueError:
                # Expected behavior - dangerous parameter should be rejected
                pass
            
            self.log_test("Secure Database Operations", True, "Database security tests passed")
            return True
            
        except Exception as e:
            self.log_test("Secure Database Operations", False, f"Exception: {str(e)}")
            return False
    
    def test_session_security(self):
        """Test session security features"""
        print("\n🧪 Testing Session Security...")
        
        try:
            # Test secure token generation
            token1 = security_manager.generate_secure_token(32)
            token2 = security_manager.generate_secure_token(32)
            
            # Tokens should be different
            if token1 == token2:
                self.log_test("Session Security", False, "Generated identical tokens")
                return False
            
            # Tokens should be proper length
            if len(token1) < 32 or len(token2) < 32:
                self.log_test("Session Security", False, "Generated tokens too short")
                return False
            
            self.log_test("Session Security", True, "Session security tests passed")
            return True
            
        except Exception as e:
            self.log_test("Session Security", False, f"Exception: {str(e)}")
            return False
    
    def test_audit_logging(self):
        """Test audit logging functionality"""
        print("\n🧪 Testing Audit Logging...")
        
        try:
            # Test audit log creation
            auth_manager.log_audit_event(
                "test_user_id",
                "TEST_ACTION",
                "test_resource",
                "Test audit log entry"
            )
            
            self.log_test("Audit Logging", True, "Audit logging tests passed")
            return True
            
        except Exception as e:
            self.log_test("Audit Logging", False, f"Exception: {str(e)}")
            return False
    
    def test_data_masking(self):
        """Test data masking functionality"""
        print("\n🧪 Testing Data Masking...")
        
        try:
            # Test SSN masking
            ssn = "***********"
            masked_ssn = security_manager.mask_sensitive_data(ssn, "ssn")
            expected_ssn = "***-**-6789"
            
            if masked_ssn != expected_ssn:
                self.log_test("Data Masking", False, f"SSN masking failed: {masked_ssn}")
                return False
            
            # Test phone masking
            phone = "************"
            masked_phone = security_manager.mask_sensitive_data(phone, "phone")
            expected_phone = "***-***-4567"
            
            if masked_phone != expected_phone:
                self.log_test("Data Masking", False, f"Phone masking failed: {masked_phone}")
                return False
            
            # Test email masking
            email = "<EMAIL>"
            masked_email = security_manager.mask_sensitive_data(email, "email")
            
            if not masked_email.startswith("t***@"):
                self.log_test("Data Masking", False, f"Email masking failed: {masked_email}")
                return False
            
            self.log_test("Data Masking", True, "All data masking tests passed")
            return True
            
        except Exception as e:
            self.log_test("Data Masking", False, f"Exception: {str(e)}")
            return False
    
    def run_all_tests(self):
        """Run all security and functionality tests"""
        print("🔒 Running Comprehensive Security and Functionality Tests")
        print("=" * 80)
        
        tests = [
            ("SQL Injection Prevention", self.test_sql_injection_prevention),
            ("XSS Prevention", self.test_xss_prevention),
            ("Authentication System", self.test_authentication_system),
            ("Data Encryption", self.test_data_encryption),
            ("Input Validation", self.test_input_validation),
            ("Secure Database Operations", self.test_secure_database_operations),
            ("Session Security", self.test_session_security),
            ("Audit Logging", self.test_audit_logging),
            ("Data Masking", self.test_data_masking),
        ]
        
        start_time = time.time()
        
        for test_name, test_func in tests:
            try:
                test_func()
            except Exception as e:
                self.log_test(test_name, False, f"Unexpected error: {str(e)}")
        
        end_time = time.time()
        duration = end_time - start_time
        
        print("\n" + "=" * 80)
        print(f"📊 Test Results Summary")
        print(f"Total Tests: {self.passed_tests + self.failed_tests}")
        print(f"Passed: {self.passed_tests}")
        print(f"Failed: {self.failed_tests}")
        print(f"Success Rate: {(self.passed_tests / (self.passed_tests + self.failed_tests) * 100):.1f}%")
        print(f"Duration: {duration:.2f} seconds")
        
        if self.failed_tests == 0:
            print("\n🎉 ALL SECURITY TESTS PASSED!")
            print("✅ SQL injection prevention: IMPLEMENTED AND TESTED")
            print("✅ XSS attack prevention: IMPLEMENTED AND TESTED")
            print("✅ Authentication system: IMPLEMENTED AND TESTED")
            print("✅ Data encryption: IMPLEMENTED AND TESTED")
            print("✅ Input validation: IMPLEMENTED AND TESTED")
            print("✅ Secure database operations: IMPLEMENTED AND TESTED")
            print("✅ Session security: IMPLEMENTED AND TESTED")
            print("✅ Audit logging: IMPLEMENTED AND TESTED")
            print("✅ Data masking: IMPLEMENTED AND TESTED")
            print("\n🔒 SYSTEM IS READY FOR PRODUCTION USE")
        else:
            print(f"\n⚠️ {self.failed_tests} security tests failed. Please review and fix issues before production use.")
        
        # Save test results
        with open(f'security_test_results_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json', 'w') as f:
            json.dump(self.test_results, f, indent=2)
        
        return self.failed_tests == 0

if __name__ == "__main__":
    tester = ComprehensiveSecurityTest()
    success = tester.run_all_tests()
    sys.exit(0 if success else 1)
