#!/usr/bin/env python3
"""
Comprehensive Security Manager for Psychiatric Assessment System
Implements SQL injection prevention, XSS protection, input validation, and data sanitization
"""

import re
import html
import hashlib
import secrets
import logging
import sqlite3
from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timedelta
import json
import bleach
from cryptography.fernet import <PERSON><PERSON><PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64
import os

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SecurityManager:
    """Comprehensive security manager for the psychiatric assessment system"""
    
    def __init__(self):
        self.encryption_key = self._get_or_create_encryption_key()
        self.cipher_suite = Fernet(self.encryption_key)
        
        # SQL injection patterns to detect and prevent
        self.sql_injection_patterns = [
            r"';.*--",  # SQL comment injection
            r"'\s+OR\s+",  # OR injection
            r"'\s+UNION\s+",  # UNION injection
            r"DROP\s+TABLE",  # DROP TABLE
            r"DELETE\s+FROM",  # DELETE FROM
            r"INSERT\s+INTO",  # INSERT INTO
            r"UPDATE\s+.*SET",  # UPDATE SET
            r"EXEC\s*\(",  # EXEC function
            r"EXECUTE\s*\(",  # EXECUTE function
            r"xp_cmdshell",  # Command shell
            r"sp_executesql",  # Dynamic SQL
        ]
        
        # XSS patterns to detect and prevent
        self.xss_patterns = [
            r"<script.*?>.*?</script>",
            r"javascript:",
            r"on\w+\s*=",
            r"<.*?on\w+.*?>",
            r"<svg.*?onload.*?>",
            r"<iframe.*?>",
            r"<object.*?>",
            r"<embed.*?>",
            r"<link.*?>",
            r"<meta.*?>",
        ]
        
        # Allowed HTML tags for rich text (very restrictive)
        self.allowed_tags = ['p', 'br', 'strong', 'em', 'u', 'ol', 'ul', 'li']
        self.allowed_attributes = {}
    
    def _get_or_create_encryption_key(self) -> bytes:
        """Get or create encryption key for data encryption"""
        key_file = 'encryption.key'
        
        if os.path.exists(key_file):
            with open(key_file, 'rb') as f:
                return f.read()
        else:
            # Generate new key
            password = os.environ.get('ENCRYPTION_PASSWORD', 'default_password_change_in_production')
            password_bytes = password.encode()
            salt = os.urandom(16)
            
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                iterations=100000,
            )
            key = base64.urlsafe_b64encode(kdf.derive(password_bytes))
            
            # Save key and salt
            with open(key_file, 'wb') as f:
                f.write(key)
            with open('salt.key', 'wb') as f:
                f.write(salt)
            
            return key
    
    def validate_sql_input(self, input_text: str) -> bool:
        """Validate input for SQL injection attempts"""
        if not isinstance(input_text, str):
            return True

        # Check for dangerous SQL patterns in user input (not in legitimate queries)
        dangerous_user_patterns = [
            r"';.*--",  # SQL comment injection
            r"'\s+OR\s+",  # OR injection
            r"'\s+UNION\s+",  # UNION injection
            r"admin'--",  # Admin bypass attempt
            r"xp_cmdshell",  # Command shell
            r"sp_executesql",  # Dynamic SQL
        ]

        for pattern in dangerous_user_patterns:
            if re.search(pattern, input_text, re.IGNORECASE):
                logger.warning(f"SQL injection attempt detected: {pattern}")
                return False

        return True
    
    def sanitize_sql_input(self, input_text: str) -> str:
        """Sanitize input to prevent SQL injection"""
        if not isinstance(input_text, str):
            return str(input_text) if input_text is not None else ""
        
        # Remove dangerous SQL patterns
        sanitized = input_text
        for pattern in self.sql_injection_patterns:
            sanitized = re.sub(pattern, '', sanitized, flags=re.IGNORECASE)
        
        # Additional sanitization
        sanitized = sanitized.replace("'", "''")  # Escape single quotes
        sanitized = sanitized.replace(";", "")    # Remove semicolons
        sanitized = sanitized.replace("--", "")   # Remove SQL comments
        
        return sanitized.strip()
    
    def validate_xss_input(self, input_text: str) -> bool:
        """Validate input for XSS attempts"""
        if not isinstance(input_text, str):
            return True

        # More precise XSS patterns to avoid false positives
        dangerous_xss_patterns = [
            r"<script[^>]*>.*?</script>",
            r"javascript\s*:",
            r"on\w+\s*=\s*[\"'][^\"']*[\"']",
            r"<svg[^>]*onload[^>]*>",
            r"<iframe[^>]*>",
        ]

        for pattern in dangerous_xss_patterns:
            if re.search(pattern, input_text, re.IGNORECASE | re.DOTALL):
                logger.warning(f"XSS attempt detected: {pattern}")
                return False

        return True
    
    def sanitize_html_input(self, input_text: str) -> str:
        """Sanitize HTML input to prevent XSS"""
        if not isinstance(input_text, str):
            return str(input_text) if input_text is not None else ""
        
        # Use bleach to clean HTML
        cleaned = bleach.clean(
            input_text,
            tags=self.allowed_tags,
            attributes=self.allowed_attributes,
            strip=True
        )
        
        # Additional XSS prevention
        cleaned = html.escape(cleaned, quote=True)
        
        return cleaned
    
    def validate_and_sanitize_input(self, input_text: str, field_name: str = "input") -> tuple[bool, str]:
        """Comprehensive input validation and sanitization"""
        if not isinstance(input_text, str):
            input_text = str(input_text) if input_text is not None else ""
        
        # Check for SQL injection
        if not self.validate_sql_input(input_text):
            logger.warning(f"SQL injection attempt in {field_name}: {input_text[:100]}")
            return False, "Invalid input detected"
        
        # Check for XSS
        if not self.validate_xss_input(input_text):
            logger.warning(f"XSS attempt in {field_name}: {input_text[:100]}")
            return False, "Invalid input detected"
        
        # Sanitize the input
        sanitized = self.sanitize_html_input(input_text)
        sanitized = self.sanitize_sql_input(sanitized)
        
        return True, sanitized
    
    def encrypt_sensitive_data(self, data: str) -> str:
        """Encrypt sensitive data using AES-256"""
        if not isinstance(data, str):
            data = str(data)
        
        encrypted_data = self.cipher_suite.encrypt(data.encode())
        return base64.urlsafe_b64encode(encrypted_data).decode()
    
    def decrypt_sensitive_data(self, encrypted_data: str) -> str:
        """Decrypt sensitive data"""
        try:
            encrypted_bytes = base64.urlsafe_b64decode(encrypted_data.encode())
            decrypted_data = self.cipher_suite.decrypt(encrypted_bytes)
            return decrypted_data.decode()
        except Exception as e:
            logger.error(f"Decryption failed: {e}")
            return ""
    
    def hash_password(self, password: str, salt: Optional[str] = None) -> tuple[str, str]:
        """Hash password using PBKDF2 with SHA-256"""
        if salt is None:
            salt = secrets.token_hex(16)
        
        password_hash = hashlib.pbkdf2_hmac(
            'sha256',
            password.encode('utf-8'),
            salt.encode('utf-8'),
            100000  # iterations
        )
        
        return password_hash.hex(), salt
    
    def verify_password(self, password: str, password_hash: str, salt: str) -> bool:
        """Verify password against hash"""
        computed_hash, _ = self.hash_password(password, salt)
        return secrets.compare_digest(computed_hash, password_hash)
    
    def generate_secure_token(self, length: int = 32) -> str:
        """Generate cryptographically secure token"""
        return secrets.token_urlsafe(length)
    
    def validate_patient_id(self, patient_id: str) -> bool:
        """Validate patient ID format"""
        if not isinstance(patient_id, str):
            return False
        
        # Patient ID should be alphanumeric with hyphens, 3-50 characters
        pattern = r'^[A-Za-z0-9\-]{3,50}$'
        return bool(re.match(pattern, patient_id))
    
    def validate_age(self, age: Union[int, str]) -> bool:
        """Validate age input"""
        try:
            age_int = int(age)
            return 0 <= age_int <= 150
        except (ValueError, TypeError):
            return False
    
    def validate_email(self, email: str) -> bool:
        """Validate email format"""
        if not isinstance(email, str):
            return False
        
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return bool(re.match(pattern, email))
    
    def validate_phone(self, phone: str) -> bool:
        """Validate phone number format"""
        if not isinstance(phone, str):
            return False
        
        # Remove all non-digit characters
        digits_only = re.sub(r'\D', '', phone)
        
        # Should be 10-15 digits
        return 10 <= len(digits_only) <= 15
    
    def mask_sensitive_data(self, data: str, data_type: str = "general") -> str:
        """Mask sensitive data for display"""
        if not isinstance(data, str) or not data:
            return "***"
        
        if data_type == "ssn":
            return f"***-**-{data[-4:]}" if len(data) >= 4 else "***-**-****"
        elif data_type == "phone":
            return f"***-***-{data[-4:]}" if len(data) >= 4 else "***-***-****"
        elif data_type == "email":
            if "@" in data:
                local, domain = data.split("@", 1)
                return f"{local[0]}***@{domain}"
            return "***@***.***"
        else:
            # General masking - show first and last character
            if len(data) <= 2:
                return "*" * len(data)
            return f"{data[0]}{'*' * (len(data) - 2)}{data[-1]}"

class SecureDatabase:
    """Secure database operations with SQL injection prevention"""
    
    def __init__(self, db_path: str = 'psychiatric_assessments.db'):
        self.db_path = db_path
        self.security_manager = SecurityManager()
    
    def get_connection(self) -> Optional[sqlite3.Connection]:
        """Get secure database connection"""
        try:
            conn = sqlite3.connect(
                self.db_path,
                timeout=30.0,
                check_same_thread=False
            )
            conn.execute("PRAGMA foreign_keys = ON")
            conn.execute("PRAGMA journal_mode = WAL")
            return conn
        except sqlite3.Error as e:
            logger.error(f"Database connection failed: {e}")
            return None
    
    def execute_query(self, query: str, params: tuple = (), fetch: str = "none") -> Any:
        """Execute parameterized query safely"""
        # Only validate user input parameters, not the query itself (which is from our code)
        for param in params:
            if isinstance(param, str) and not self.security_manager.validate_sql_input(param):
                raise ValueError(f"Parameter contains potentially dangerous SQL patterns: {param}")

        conn = self.get_connection()
        if not conn:
            raise ConnectionError("Failed to connect to database")

        try:
            cursor = conn.cursor()
            cursor.execute(query, params)

            if fetch == "one":
                result = cursor.fetchone()
            elif fetch == "all":
                result = cursor.fetchall()
            else:
                result = cursor.rowcount

            conn.commit()
            return result

        except sqlite3.Error as e:
            conn.rollback()
            logger.error(f"Database query failed: {e}")
            raise
        finally:
            conn.close()
    
    def validate_and_sanitize_params(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and sanitize all parameters"""
        sanitized_params = {}
        
        for key, value in params.items():
            if isinstance(value, str):
                is_valid, sanitized_value = self.security_manager.validate_and_sanitize_input(value, key)
                if not is_valid:
                    raise ValueError(f"Invalid input in field: {key}")
                sanitized_params[key] = sanitized_value
            else:
                sanitized_params[key] = value
        
        return sanitized_params

# Global security manager instance
security_manager = SecurityManager()
secure_db = SecureDatabase()
