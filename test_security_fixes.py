#!/usr/bin/env python3
"""
Test script to verify security fixes are working correctly
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from security_manager import security_manager
from auth_manager import auth_manager, User<PERSON><PERSON>

def test_sql_injection_prevention():
    """Test SQL injection prevention"""
    print("🧪 Testing SQL Injection Prevention...")
    
    injection_attempts = [
        "'; DROP TABLE patients; --",
        "' OR '1'='1",
        "' UNION SELECT * FROM patients --",
        "'; INSERT INTO patients VALUES ('hack', 0, 'hacker'); --",
        "' OR 1=1 --",
        "admin'--",
        "' OR 'x'='x",
        "'; DELETE FROM assessments; --"
    ]
    
    passed = 0
    for attempt in injection_attempts:
        is_valid = security_manager.validate_sql_input(attempt)
        if not is_valid:
            passed += 1
            print(f"✅ Blocked: {attempt[:30]}...")
        else:
            print(f"❌ Allowed: {attempt[:30]}...")
    
    print(f"SQL Injection Prevention: {passed}/{len(injection_attempts)} blocked")
    return passed == len(injection_attempts)

def test_xss_prevention():
    """Test XSS prevention"""
    print("\n🧪 Testing XSS Prevention...")
    
    xss_attempts = [
        "<script>alert('XSS')</script>",
        "javascript:alert('XSS')",
        "<img src=x onerror=alert('XSS')>",
        "<svg onload=alert('XSS')>",
        "';alert('XSS');//"
    ]
    
    passed = 0
    for attempt in xss_attempts:
        is_valid = security_manager.validate_xss_input(attempt)
        if not is_valid:
            passed += 1
            print(f"✅ Blocked: {attempt[:30]}...")
        else:
            print(f"❌ Allowed: {attempt[:30]}...")
    
    print(f"XSS Prevention: {passed}/{len(xss_attempts)} blocked")
    return passed == len(xss_attempts)

def test_input_sanitization():
    """Test input sanitization"""
    print("\n🧪 Testing Input Sanitization...")
    
    test_inputs = [
        ("Normal text", True),
        ("'; DROP TABLE users; --", False),
        ("<script>alert('xss')</script>", False),
        ("Patient with O'Connor surname", True),
        ("javascript:void(0)", False)
    ]
    
    passed = 0
    for input_text, should_be_valid in test_inputs:
        is_valid, sanitized = security_manager.validate_and_sanitize_input(input_text)
        
        if is_valid == should_be_valid:
            passed += 1
            print(f"✅ {input_text[:30]}... -> Valid: {is_valid}")
        else:
            print(f"❌ {input_text[:30]}... -> Expected: {should_be_valid}, Got: {is_valid}")
    
    print(f"Input Sanitization: {passed}/{len(test_inputs)} correct")
    return passed == len(test_inputs)

def test_password_security():
    """Test password hashing and verification"""
    print("\n🧪 Testing Password Security...")
    
    password = "test_password_123"
    password_hash, salt = security_manager.hash_password(password)
    
    # Test correct password
    is_valid = security_manager.verify_password(password, password_hash, salt)
    if is_valid:
        print("✅ Correct password verification")
    else:
        print("❌ Correct password verification failed")
        return False
    
    # Test incorrect password
    is_valid = security_manager.verify_password("wrong_password", password_hash, salt)
    if not is_valid:
        print("✅ Incorrect password rejected")
    else:
        print("❌ Incorrect password accepted")
        return False
    
    return True

def test_data_encryption():
    """Test data encryption"""
    print("\n🧪 Testing Data Encryption...")
    
    sensitive_data = "Patient ID: SENSITIVE-123"
    
    # Encrypt data
    encrypted = security_manager.encrypt_sensitive_data(sensitive_data)
    print(f"✅ Data encrypted: {encrypted[:20]}...")
    
    # Decrypt data
    decrypted = security_manager.decrypt_sensitive_data(encrypted)
    
    if decrypted == sensitive_data:
        print("✅ Data decryption successful")
        return True
    else:
        print(f"❌ Data decryption failed: {decrypted}")
        return False

def test_validation_functions():
    """Test various validation functions"""
    print("\n🧪 Testing Validation Functions...")
    
    tests = [
        ("Patient ID", security_manager.validate_patient_id, "PATIENT-123", True),
        ("Patient ID", security_manager.validate_patient_id, "'; DROP TABLE", False),
        ("Age", security_manager.validate_age, 25, True),
        ("Age", security_manager.validate_age, -5, False),
        ("Age", security_manager.validate_age, 200, False),
        ("Email", security_manager.validate_email, "<EMAIL>", True),
        ("Email", security_manager.validate_email, "invalid-email", False),
        ("Phone", security_manager.validate_phone, "************", True),
        ("Phone", security_manager.validate_phone, "123", False),
    ]
    
    passed = 0
    for test_name, func, value, expected in tests:
        result = func(value)
        if result == expected:
            passed += 1
            print(f"✅ {test_name}: {value} -> {result}")
        else:
            print(f"❌ {test_name}: {value} -> Expected: {expected}, Got: {result}")
    
    print(f"Validation Functions: {passed}/{len(tests)} correct")
    return passed == len(tests)

def test_user_registration():
    """Test user registration with security validation"""
    print("\n🧪 Testing User Registration...")
    
    # Test valid registration
    success, message = auth_manager.register_user(
        "test_user", 
        "<EMAIL>", 
        "secure_password_123", 
        UserRole.VIEWER
    )
    
    if success:
        print("✅ Valid user registration successful")
    else:
        print(f"❌ Valid user registration failed: {message}")
        return False
    
    # Test duplicate registration
    success, message = auth_manager.register_user(
        "test_user", 
        "<EMAIL>", 
        "secure_password_123", 
        UserRole.VIEWER
    )
    
    if not success:
        print("✅ Duplicate user registration rejected")
    else:
        print("❌ Duplicate user registration allowed")
        return False
    
    # Test invalid email
    success, message = auth_manager.register_user(
        "test_user2", 
        "invalid-email", 
        "secure_password_123", 
        UserRole.VIEWER
    )
    
    if not success:
        print("✅ Invalid email registration rejected")
    else:
        print("❌ Invalid email registration allowed")
        return False
    
    return True

def run_all_security_tests():
    """Run all security tests"""
    print("🔒 Running Comprehensive Security Tests")
    print("=" * 60)
    
    tests = [
        ("SQL Injection Prevention", test_sql_injection_prevention),
        ("XSS Prevention", test_xss_prevention),
        ("Input Sanitization", test_input_sanitization),
        ("Password Security", test_password_security),
        ("Data Encryption", test_data_encryption),
        ("Validation Functions", test_validation_functions),
        ("User Registration", test_user_registration),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"\n✅ {test_name}: PASSED")
            else:
                failed += 1
                print(f"\n❌ {test_name}: FAILED")
        except Exception as e:
            failed += 1
            print(f"\n❌ {test_name}: ERROR - {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 Security Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All security tests passed!")
        print("✅ SQL injection prevention: IMPLEMENTED")
        print("✅ XSS attack prevention: IMPLEMENTED")
        print("✅ Input validation and sanitization: IMPLEMENTED")
        print("✅ Password security: IMPLEMENTED")
        print("✅ Data encryption: IMPLEMENTED")
        print("✅ User authentication: IMPLEMENTED")
    else:
        print("⚠️ Some security tests failed. Please review the issues above.")
    
    return failed == 0

if __name__ == "__main__":
    success = run_all_security_tests()
    sys.exit(0 if success else 1)
