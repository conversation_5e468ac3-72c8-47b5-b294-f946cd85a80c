[{"test": "SQL Injection Prevention", "passed": false, "message": "Blocked 7/8 injection attempts", "timestamp": "2025-08-24T22:16:04.762725"}, {"test": "XSS Prevention", "passed": false, "message": "Blocked 4/5 XSS attempts", "timestamp": "2025-08-24T22:16:04.797231"}, {"test": "User Registration", "passed": false, "message": "Registration failed: Registration failed", "timestamp": "2025-08-24T22:16:04.953656"}, {"test": "Data Encryption", "passed": true, "message": "All encryption tests passed", "timestamp": "2025-08-24T22:16:05.463218"}, {"test": "Input Validation", "passed": true, "message": "All validation tests passed", "timestamp": "2025-08-24T22:16:05.464156"}, {"test": "Secure Database Operations", "passed": true, "message": "Database security tests passed", "timestamp": "2025-08-24T22:16:05.536468"}, {"test": "Session Security", "passed": true, "message": "Session security tests passed", "timestamp": "2025-08-24T22:16:05.536937"}, {"test": "<PERSON>t Logging", "passed": true, "message": "Audit logging tests passed", "timestamp": "2025-08-24T22:16:05.538773"}, {"test": "Data Masking", "passed": true, "message": "All data masking tests passed", "timestamp": "2025-08-24T22:16:05.539310"}]