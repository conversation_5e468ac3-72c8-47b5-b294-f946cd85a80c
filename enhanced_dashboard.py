#!/usr/bin/env python3
"""
Enhanced Dashboard with Real-time Analytics and Improved UI
Implements advanced filtering, export functionality, and clinical decision support
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import datetime
from typing import Dict, Any, List, Optional
import json
import io
from reportlab.lib.pagesizes import letter
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib import colors
import base64

from security_manager import security_manager
from auth_manager import auth_manager, Permission

class EnhancedDashboard:
    """Enhanced dashboard with advanced analytics and UI improvements"""
    
    def __init__(self):
        self.risk_colors = {
            'Low': '#10b981',
            'Moderate': '#f59e0b', 
            'High': '#ef4444',
            'Imminent': '#dc2626'
        }
        
        self.diagnosis_categories = {
            'Mood Disorders': ['Major Depressive Disorder', 'Bipolar I Disorder', 'Bipolar II Disorder', 'Dysthymic Disorder'],
            'Anxiety Disorders': ['Generalized Anxiety Disorder', 'Panic Disorder', 'Social Anxiety Disorder', 'Specific Phobia'],
            'Trauma Disorders': ['PTSD', 'Acute Stress Disorder', 'Adjustment Disorder'],
            'Psychotic Disorders': ['Schizophrenia', 'Brief Psychotic Disorder', 'Delusional Disorder'],
            'Substance Use': ['Alcohol Use Disorder', 'Substance Use Disorder', 'Cannabis Use Disorder'],
            'Other': ['ADHD', 'OCD', 'Eating Disorder', 'Personality Disorder', 'Other']
        }
    
    def show_dashboard_header(self):
        """Display dashboard header with user info and controls"""
        current_user = auth_manager.get_current_user()
        
        st.markdown('<h1 class="main-header">📊 Clinical Analytics Dashboard</h1>', unsafe_allow_html=True)
        
        # User info and controls
        col1, col2, col3, col4 = st.columns([2, 1, 1, 1])
        
        with col1:
            st.markdown(f"**Welcome, {current_user['username']}** | Role: {current_user['role'].value.title()}")
        
        with col2:
            if st.button("🔄 Refresh Data"):
                st.cache_data.clear()
                st.rerun()
        
        with col3:
            if st.button("🧠 New Assessment"):
                st.session_state.current_view = 'assessment'
                st.rerun()
        
        with col4:
            auto_refresh = st.checkbox("Auto-refresh", value=False)
            if auto_refresh:
                st.rerun()
    
    def show_real_time_metrics(self, assessments_df: pd.DataFrame):
        """Display real-time metrics cards"""
        st.markdown('<div class="section-header">📈 Real-time Metrics</div>', unsafe_allow_html=True)
        
        # Calculate metrics
        total_assessments = len(assessments_df)
        total_patients = assessments_df['patient_id'].nunique() if not assessments_df.empty else 0
        
        # Risk level counts
        high_risk_count = len(assessments_df[assessments_df['suicide_risk_level'].isin(['High', 'Imminent'])]) if not assessments_df.empty else 0
        
        # Average scores
        avg_phq9 = assessments_df['phq9_score'].mean() if not assessments_df.empty and 'phq9_score' in assessments_df.columns else 0
        avg_gad7 = assessments_df['gad7_score'].mean() if not assessments_df.empty and 'gad7_score' in assessments_df.columns else 0
        
        # Completion rate
        avg_completion = assessments_df['completion_percentage'].mean() if not assessments_df.empty else 0
        
        # Display metrics in cards
        col1, col2, col3, col4, col5, col6 = st.columns(6)
        
        with col1:
            st.markdown(f"""
            <div class="metric-card">
                <h3>{total_patients}</h3>
                <p>Total Patients</p>
            </div>
            """, unsafe_allow_html=True)
        
        with col2:
            st.markdown(f"""
            <div class="metric-card">
                <h3>{total_assessments}</h3>
                <p>Total Assessments</p>
            </div>
            """, unsafe_allow_html=True)
        
        with col3:
            risk_class = "risk-high" if high_risk_count > 0 else "risk-low"
            st.markdown(f"""
            <div class="metric-card {risk_class}">
                <h3>{high_risk_count}</h3>
                <p>High Risk Cases</p>
            </div>
            """, unsafe_allow_html=True)
        
        with col4:
            st.markdown(f"""
            <div class="metric-card">
                <h3>{avg_phq9:.1f}</h3>
                <p>Avg PHQ-9 Score</p>
            </div>
            """, unsafe_allow_html=True)
        
        with col5:
            st.markdown(f"""
            <div class="metric-card">
                <h3>{avg_gad7:.1f}</h3>
                <p>Avg GAD-7 Score</p>
            </div>
            """, unsafe_allow_html=True)
        
        with col6:
            completion_class = "risk-high" if avg_completion < 80 else "risk-low"
            st.markdown(f"""
            <div class="metric-card {completion_class}">
                <h3>{avg_completion:.1f}%</h3>
                <p>Avg Completion</p>
            </div>
            """, unsafe_allow_html=True)
    
    def show_advanced_filters(self) -> Dict[str, Any]:
        """Display advanced filtering options"""
        st.markdown('<div class="section-header">🔍 Advanced Filters</div>', unsafe_allow_html=True)
        
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            # Date range filter
            st.markdown("**Date Range**")
            date_from = st.date_input("From", value=datetime.date.today() - datetime.timedelta(days=30))
            date_to = st.date_input("To", value=datetime.date.today())
        
        with col2:
            # Risk level filter
            st.markdown("**Risk Level**")
            risk_levels = st.multiselect(
                "Select risk levels",
                ["Low", "Moderate", "High", "Imminent"],
                default=[]
            )
        
        with col3:
            # Diagnosis category filter
            st.markdown("**Diagnosis Category**")
            diagnosis_categories = st.multiselect(
                "Select categories",
                list(self.diagnosis_categories.keys()),
                default=[]
            )
        
        with col4:
            # Score range filters
            st.markdown("**Score Ranges**")
            phq9_range = st.slider("PHQ-9 Score Range", 0, 27, (0, 27))
            gad7_range = st.slider("GAD-7 Score Range", 0, 21, (0, 21))
        
        # Additional filters
        col1, col2, col3 = st.columns(3)
        
        with col1:
            age_range = st.slider("Age Range", 0, 100, (0, 100))
        
        with col2:
            gender_filter = st.multiselect(
                "Gender",
                ["Male", "Female", "Non-binary", "Other"],
                default=[]
            )
        
        with col3:
            completion_threshold = st.slider("Min Completion %", 0, 100, 0)
        
        return {
            'date_from': date_from,
            'date_to': date_to,
            'risk_levels': risk_levels,
            'diagnosis_categories': diagnosis_categories,
            'phq9_range': phq9_range,
            'gad7_range': gad7_range,
            'age_range': age_range,
            'gender_filter': gender_filter,
            'completion_threshold': completion_threshold
        }
    
    def apply_filters(self, df: pd.DataFrame, filters: Dict[str, Any]) -> pd.DataFrame:
        """Apply filters to dataframe"""
        if df.empty:
            return df
        
        filtered_df = df.copy()
        
        # Date filter
        if 'assessment_date' in filtered_df.columns:
            filtered_df['assessment_date'] = pd.to_datetime(filtered_df['assessment_date'])
            filtered_df = filtered_df[
                (filtered_df['assessment_date'].dt.date >= filters['date_from']) &
                (filtered_df['assessment_date'].dt.date <= filters['date_to'])
            ]
        
        # Risk level filter
        if filters['risk_levels']:
            filtered_df = filtered_df[filtered_df['suicide_risk_level'].isin(filters['risk_levels'])]
        
        # Diagnosis category filter
        if filters['diagnosis_categories']:
            selected_diagnoses = []
            for category in filters['diagnosis_categories']:
                selected_diagnoses.extend(self.diagnosis_categories[category])
            filtered_df = filtered_df[filtered_df['primary_diagnosis'].isin(selected_diagnoses)]
        
        # Score range filters
        if 'phq9_score' in filtered_df.columns:
            filtered_df = filtered_df[
                (filtered_df['phq9_score'].between(filters['phq9_range'][0], filters['phq9_range'][1])) |
                (filtered_df['phq9_score'].isna())
            ]
        
        if 'gad7_score' in filtered_df.columns:
            filtered_df = filtered_df[
                (filtered_df['gad7_score'].between(filters['gad7_range'][0], filters['gad7_range'][1])) |
                (filtered_df['gad7_score'].isna())
            ]
        
        # Age filter
        if 'age' in filtered_df.columns:
            filtered_df = filtered_df[
                (filtered_df['age'].between(filters['age_range'][0], filters['age_range'][1])) |
                (filtered_df['age'].isna())
            ]
        
        # Gender filter
        if filters['gender_filter'] and 'gender' in filtered_df.columns:
            filtered_df = filtered_df[filtered_df['gender'].isin(filters['gender_filter'])]
        
        # Completion threshold
        if 'completion_percentage' in filtered_df.columns:
            filtered_df = filtered_df[filtered_df['completion_percentage'] >= filters['completion_threshold']]
        
        return filtered_df
    
    def show_interactive_charts(self, assessments_df: pd.DataFrame):
        """Display interactive charts and visualizations"""
        st.markdown('<div class="section-header">📊 Interactive Analytics</div>', unsafe_allow_html=True)
        
        if assessments_df.empty:
            st.warning("No data available for visualization")
            return
        
        # Create tabs for different chart types
        tab1, tab2, tab3, tab4 = st.tabs(["Risk Analysis", "Score Trends", "Demographics", "Clinical Insights"])
        
        with tab1:
            self._show_risk_analysis_charts(assessments_df)
        
        with tab2:
            self._show_score_trend_charts(assessments_df)
        
        with tab3:
            self._show_demographic_charts(assessments_df)
        
        with tab4:
            self._show_clinical_insights(assessments_df)
    
    def _show_risk_analysis_charts(self, df: pd.DataFrame):
        """Show risk analysis charts"""
        col1, col2 = st.columns(2)
        
        with col1:
            # Risk level distribution
            if 'suicide_risk_level' in df.columns:
                risk_counts = df['suicide_risk_level'].value_counts().reset_index()
                risk_counts.columns = ['Risk Level', 'Count']
                
                fig = px.pie(
                    risk_counts,
                    values='Count',
                    names='Risk Level',
                    title="Suicide Risk Level Distribution",
                    color='Risk Level',
                    color_discrete_map=self.risk_colors
                )
                fig.update_traces(textposition='inside', textinfo='percent+label')
                st.plotly_chart(fig, use_container_width=True)
        
        with col2:
            # Risk over time
            if 'assessment_date' in df.columns and 'suicide_risk_level' in df.columns:
                df['assessment_date'] = pd.to_datetime(df['assessment_date'])
                df['month'] = df['assessment_date'].dt.to_period('M').astype(str)
                
                risk_time = df.groupby(['month', 'suicide_risk_level']).size().reset_index(name='count')
                
                fig = px.bar(
                    risk_time,
                    x='month',
                    y='count',
                    color='suicide_risk_level',
                    title="Risk Levels Over Time",
                    color_discrete_map=self.risk_colors
                )
                fig.update_layout(xaxis_title="Month", yaxis_title="Number of Cases")
                st.plotly_chart(fig, use_container_width=True)
    
    def _show_score_trend_charts(self, df: pd.DataFrame):
        """Show score trend charts"""
        col1, col2 = st.columns(2)
        
        with col1:
            # PHQ-9 score distribution
            if 'phq9_score' in df.columns:
                fig = px.histogram(
                    df.dropna(subset=['phq9_score']),
                    x='phq9_score',
                    title="PHQ-9 Score Distribution",
                    nbins=20,
                    color_discrete_sequence=['#3b82f6']
                )
                fig.add_vline(x=5, line_dash="dash", line_color="orange", annotation_text="Mild")
                fig.add_vline(x=10, line_dash="dash", line_color="red", annotation_text="Moderate")
                fig.add_vline(x=15, line_dash="dash", line_color="darkred", annotation_text="Severe")
                st.plotly_chart(fig, use_container_width=True)
        
        with col2:
            # GAD-7 score distribution
            if 'gad7_score' in df.columns:
                fig = px.histogram(
                    df.dropna(subset=['gad7_score']),
                    x='gad7_score',
                    title="GAD-7 Score Distribution",
                    nbins=15,
                    color_discrete_sequence=['#8b5cf6']
                )
                fig.add_vline(x=5, line_dash="dash", line_color="orange", annotation_text="Mild")
                fig.add_vline(x=10, line_dash="dash", line_color="red", annotation_text="Moderate")
                fig.add_vline(x=15, line_dash="dash", line_color="darkred", annotation_text="Severe")
                st.plotly_chart(fig, use_container_width=True)
    
    def _show_demographic_charts(self, df: pd.DataFrame):
        """Show demographic analysis charts"""
        col1, col2 = st.columns(2)
        
        with col1:
            # Age distribution
            if 'age' in df.columns:
                fig = px.histogram(
                    df.dropna(subset=['age']),
                    x='age',
                    title="Age Distribution",
                    nbins=20,
                    color_discrete_sequence=['#10b981']
                )
                st.plotly_chart(fig, use_container_width=True)
        
        with col2:
            # Gender distribution
            if 'gender' in df.columns:
                gender_counts = df['gender'].value_counts().reset_index()
                gender_counts.columns = ['Gender', 'Count']
                
                fig = px.bar(
                    gender_counts,
                    x='Gender',
                    y='Count',
                    title="Gender Distribution",
                    color_discrete_sequence=['#f59e0b']
                )
                st.plotly_chart(fig, use_container_width=True)
    
    def _show_clinical_insights(self, df: pd.DataFrame):
        """Show clinical insights and correlations"""
        st.markdown("**Clinical Decision Support Alerts**")
        
        # High-risk patient alerts
        if 'suicide_risk_level' in df.columns:
            high_risk_patients = df[df['suicide_risk_level'].isin(['High', 'Imminent'])]
            
            if not high_risk_patients.empty:
                st.error(f"⚠️ {len(high_risk_patients)} patients require immediate attention (High/Imminent risk)")
                
                for _, patient in high_risk_patients.iterrows():
                    st.markdown(f"- **{patient['patient_id']}**: {patient['suicide_risk_level']} risk level")
        
        # Score correlation analysis
        if 'phq9_score' in df.columns and 'gad7_score' in df.columns:
            correlation_data = df[['phq9_score', 'gad7_score']].dropna()
            
            if not correlation_data.empty:
                fig = px.scatter(
                    correlation_data,
                    x='phq9_score',
                    y='gad7_score',
                    title="PHQ-9 vs GAD-7 Score Correlation",
                    trendline="ols"
                )
                st.plotly_chart(fig, use_container_width=True)

# Global dashboard instance
enhanced_dashboard = EnhancedDashboard()
