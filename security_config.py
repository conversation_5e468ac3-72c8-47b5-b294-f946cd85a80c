#!/usr/bin/env python3
"""
Security Configuration and HTTPS Enforcement for Psychiatric Assessment System
Implements security headers, HTTPS redirection, and secure configuration
"""

import streamlit as st
import os
from typing import Dict, Any
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SecurityConfig:
    """Security configuration and enforcement"""
    
    def __init__(self):
        self.security_headers = {
            'X-Content-Type-Options': 'nosniff',
            'X-Frame-Options': 'DENY',
            'X-XSS-Protection': '1; mode=block',
            'Strict-Transport-Security': 'max-age=31536000; includeSubDomains; preload',
            'Content-Security-Policy': self._get_csp_policy(),
            'Referrer-Policy': 'strict-origin-when-cross-origin',
            'Permissions-Policy': 'geolocation=(), microphone=(), camera=()',
            'X-Permitted-Cross-Domain-Policies': 'none'
        }
    
    def _get_csp_policy(self) -> str:
        """Get Content Security Policy"""
        return (
            "default-src 'self'; "
            "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.plot.ly; "
            "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; "
            "font-src 'self' https://fonts.gstatic.com; "
            "img-src 'self' data: https:; "
            "connect-src 'self' https:; "
            "frame-ancestors 'none'; "
            "base-uri 'self'; "
            "form-action 'self'"
        )
    
    def apply_security_headers(self):
        """Apply security headers to Streamlit app"""
        try:
            # Apply headers using Streamlit's HTML injection
            header_html = ""
            for header, value in self.security_headers.items():
                header_html += f'<meta http-equiv="{header}" content="{value}">\n'
            
            st.markdown(f"""
            <head>
                {header_html}
            </head>
            """, unsafe_allow_html=True)
            
            # Additional security configurations
            st.markdown("""
            <script>
                // Disable right-click context menu for security
                document.addEventListener('contextmenu', function(e) {
                    e.preventDefault();
                });
                
                // Disable F12 and other developer tools shortcuts
                document.addEventListener('keydown', function(e) {
                    if (e.key === 'F12' || 
                        (e.ctrlKey && e.shiftKey && e.key === 'I') ||
                        (e.ctrlKey && e.shiftKey && e.key === 'C') ||
                        (e.ctrlKey && e.shiftKey && e.key === 'J') ||
                        (e.ctrlKey && e.key === 'U')) {
                        e.preventDefault();
                    }
                });
                
                // Clear sensitive data on page unload
                window.addEventListener('beforeunload', function() {
                    sessionStorage.clear();
                    // Clear any sensitive form data
                    const forms = document.querySelectorAll('form');
                    forms.forEach(form => form.reset());
                });
            </script>
            """, unsafe_allow_html=True)
            
        except Exception as e:
            logger.error(f"Failed to apply security headers: {e}")
    
    def enforce_https(self):
        """Enforce HTTPS and secure cookies"""
        try:
            # Check if running in production (not localhost)
            if not self._is_localhost():
                # Force HTTPS redirect
                st.markdown("""
                <script>
                    if (location.protocol !== 'https:') {
                        location.replace('https:' + window.location.href.substring(window.location.protocol.length));
                    }
                </script>
                """, unsafe_allow_html=True)
            
            # Set secure cookie attributes
            st.markdown("""
            <script>
                // Override document.cookie to ensure secure attributes
                const originalCookieDescriptor = Object.getOwnPropertyDescriptor(Document.prototype, 'cookie') || 
                                                Object.getOwnPropertyDescriptor(HTMLDocument.prototype, 'cookie');
                
                if (originalCookieDescriptor && originalCookieDescriptor.configurable) {
                    Object.defineProperty(document, 'cookie', {
                        get: originalCookieDescriptor.get,
                        set: function(cookieString) {
                            // Add secure attributes to all cookies
                            if (!cookieString.includes('Secure') && location.protocol === 'https:') {
                                cookieString += '; Secure';
                            }
                            if (!cookieString.includes('HttpOnly')) {
                                cookieString += '; HttpOnly';
                            }
                            if (!cookieString.includes('SameSite')) {
                                cookieString += '; SameSite=Strict';
                            }
                            originalCookieDescriptor.set.call(this, cookieString);
                        },
                        enumerable: true,
                        configurable: true
                    });
                }
            </script>
            """, unsafe_allow_html=True)
            
        except Exception as e:
            logger.error(f"Failed to enforce HTTPS: {e}")
    
    def _is_localhost(self) -> bool:
        """Check if running on localhost"""
        host = os.environ.get('STREAMLIT_SERVER_ADDRESS', 'localhost')
        return host in ['localhost', '127.0.0.1', '0.0.0.0']
    
    def configure_streamlit_security(self):
        """Configure Streamlit-specific security settings"""
        try:
            # Set Streamlit configuration for security
            st.set_page_config(
                page_title="Psychiatric Assessment System",
                page_icon="🧠",
                layout="wide",
                initial_sidebar_state="expanded",
                menu_items={
                    'Get Help': None,
                    'Report a bug': None,
                    'About': "Secure Psychiatric Assessment System"
                }
            )
            
            # Hide Streamlit menu and footer for security
            hide_streamlit_style = """
            <style>
                #MainMenu {visibility: hidden;}
                footer {visibility: hidden;}
                header {visibility: hidden;}
                .stDeployButton {display:none;}
                .stDecoration {display:none;}
                
                /* Additional security styling */
                .stApp {
                    background-color: #f8f9fa;
                }
                
                /* Secure form styling */
                .stTextInput > div > div > input[type="password"] {
                    -webkit-text-security: disc;
                    text-security: disc;
                }
                
                /* Disable text selection for sensitive areas */
                .no-select {
                    -webkit-user-select: none;
                    -moz-user-select: none;
                    -ms-user-select: none;
                    user-select: none;
                }
            </style>
            """
            st.markdown(hide_streamlit_style, unsafe_allow_html=True)
            
        except Exception as e:
            logger.error(f"Failed to configure Streamlit security: {e}")
    
    def add_session_timeout_warning(self, timeout_minutes: int = 30):
        """Add session timeout warning"""
        try:
            st.markdown(f"""
            <script>
                let sessionTimeout = {timeout_minutes * 60 * 1000}; // Convert to milliseconds
                let warningTime = sessionTimeout - (5 * 60 * 1000); // 5 minutes before timeout
                let lastActivity = Date.now();
                
                // Track user activity
                function resetTimer() {{
                    lastActivity = Date.now();
                }}
                
                // Add event listeners for user activity
                document.addEventListener('mousedown', resetTimer);
                document.addEventListener('mousemove', resetTimer);
                document.addEventListener('keypress', resetTimer);
                document.addEventListener('scroll', resetTimer);
                document.addEventListener('touchstart', resetTimer);
                
                // Check for session timeout
                function checkTimeout() {{
                    let now = Date.now();
                    let timeSinceActivity = now - lastActivity;
                    
                    if (timeSinceActivity >= sessionTimeout) {{
                        alert('Your session has expired. You will be logged out for security.');
                        window.location.reload();
                    }} else if (timeSinceActivity >= warningTime) {{
                        let remainingMinutes = Math.ceil((sessionTimeout - timeSinceActivity) / 60000);
                        if (confirm(`Your session will expire in ${{remainingMinutes}} minute(s). Click OK to extend your session.`)) {{
                            resetTimer();
                        }}
                    }}
                }}
                
                // Check every minute
                setInterval(checkTimeout, 60000);
            </script>
            """, unsafe_allow_html=True)
            
        except Exception as e:
            logger.error(f"Failed to add session timeout warning: {e}")
    
    def add_security_monitoring(self):
        """Add client-side security monitoring"""
        try:
            st.markdown("""
            <script>
                // Monitor for suspicious activity
                let suspiciousActivity = 0;
                
                // Detect rapid form submissions
                let lastSubmission = 0;
                document.addEventListener('submit', function(e) {
                    let now = Date.now();
                    if (now - lastSubmission < 1000) { // Less than 1 second
                        suspiciousActivity++;
                        if (suspiciousActivity > 5) {
                            alert('Suspicious activity detected. Please contact support.');
                            e.preventDefault();
                        }
                    }
                    lastSubmission = now;
                });
                
                // Detect multiple failed login attempts
                let failedLogins = 0;
                function recordFailedLogin() {
                    failedLogins++;
                    if (failedLogins >= 3) {
                        alert('Multiple failed login attempts detected. Please wait before trying again.');
                    }
                }
                
                // Monitor for XSS attempts in input fields
                document.addEventListener('input', function(e) {
                    let value = e.target.value;
                    let xssPatterns = [
                        /<script/i,
                        /javascript:/i,
                        /on\w+\s*=/i,
                        /<iframe/i,
                        /<object/i
                    ];
                    
                    for (let pattern of xssPatterns) {
                        if (pattern.test(value)) {
                            e.target.value = value.replace(pattern, '');
                            alert('Potentially dangerous content detected and removed.');
                            break;
                        }
                    }
                });
                
                // Detect developer tools
                let devtools = {
                    open: false,
                    orientation: null
                };
                
                setInterval(function() {
                    if (window.outerHeight - window.innerHeight > 200 || 
                        window.outerWidth - window.innerWidth > 200) {
                        if (!devtools.open) {
                            devtools.open = true;
                            console.clear();
                            console.log('%cSecurity Warning', 'color: red; font-size: 20px; font-weight: bold;');
                            console.log('%cThis is a secure medical application. Unauthorized access is prohibited.', 'color: red; font-size: 14px;');
                        }
                    } else {
                        devtools.open = false;
                    }
                }, 500);
            </script>
            """, unsafe_allow_html=True)
            
        except Exception as e:
            logger.error(f"Failed to add security monitoring: {e}")
    
    def apply_all_security_measures(self):
        """Apply all security measures"""
        self.configure_streamlit_security()
        self.apply_security_headers()
        self.enforce_https()
        self.add_session_timeout_warning()
        self.add_security_monitoring()

class SecureEnvironment:
    """Secure environment configuration"""
    
    @staticmethod
    def get_secure_config() -> Dict[str, Any]:
        """Get secure configuration settings"""
        return {
            'database': {
                'path': os.environ.get('DB_PATH', 'psychiatric_assessments.db'),
                'timeout': int(os.environ.get('DB_TIMEOUT', '30')),
                'backup_enabled': os.environ.get('DB_BACKUP_ENABLED', 'true').lower() == 'true',
                'backup_interval': int(os.environ.get('DB_BACKUP_INTERVAL', '3600'))  # 1 hour
            },
            'security': {
                'session_timeout': int(os.environ.get('SESSION_TIMEOUT', '1800')),  # 30 minutes
                'max_login_attempts': int(os.environ.get('MAX_LOGIN_ATTEMPTS', '5')),
                'lockout_duration': int(os.environ.get('LOCKOUT_DURATION', '900')),  # 15 minutes
                'password_min_length': int(os.environ.get('PASSWORD_MIN_LENGTH', '8')),
                'require_https': os.environ.get('REQUIRE_HTTPS', 'true').lower() == 'true',
                'audit_log_enabled': os.environ.get('AUDIT_LOG_ENABLED', 'true').lower() == 'true'
            },
            'encryption': {
                'algorithm': 'AES-256',
                'key_rotation_days': int(os.environ.get('KEY_ROTATION_DAYS', '90')),
                'encrypt_at_rest': os.environ.get('ENCRYPT_AT_REST', 'true').lower() == 'true'
            },
            'compliance': {
                'hipaa_mode': os.environ.get('HIPAA_MODE', 'true').lower() == 'true',
                'data_retention_days': int(os.environ.get('DATA_RETENTION_DAYS', '2555')),  # 7 years
                'audit_retention_days': int(os.environ.get('AUDIT_RETENTION_DAYS', '1095')),  # 3 years
                'breach_notification_email': os.environ.get('BREACH_NOTIFICATION_EMAIL', '<EMAIL>')
            },
            'performance': {
                'max_concurrent_users': int(os.environ.get('MAX_CONCURRENT_USERS', '50')),
                'rate_limit_requests': int(os.environ.get('RATE_LIMIT_REQUESTS', '100')),
                'rate_limit_window': int(os.environ.get('RATE_LIMIT_WINDOW', '3600'))  # 1 hour
            }
        }
    
    @staticmethod
    def validate_environment():
        """Validate security environment"""
        issues = []
        
        # Check for required environment variables in production
        if not SecureEnvironment._is_development():
            required_vars = [
                'ENCRYPTION_PASSWORD',
                'DB_PATH',
                'BREACH_NOTIFICATION_EMAIL'
            ]
            
            for var in required_vars:
                if not os.environ.get(var):
                    issues.append(f"Missing required environment variable: {var}")
        
        # Check file permissions
        sensitive_files = ['encryption.key', 'salt.key', 'psychiatric_assessments.db']
        for file_path in sensitive_files:
            if os.path.exists(file_path):
                stat_info = os.stat(file_path)
                if stat_info.st_mode & 0o077:  # Check if group/other have any permissions
                    issues.append(f"File {file_path} has overly permissive permissions")
        
        return issues
    
    @staticmethod
    def _is_development() -> bool:
        """Check if running in development mode"""
        return os.environ.get('ENVIRONMENT', 'development').lower() == 'development'

# Global security configuration instance
security_config = SecurityConfig()
secure_env = SecureEnvironment()
